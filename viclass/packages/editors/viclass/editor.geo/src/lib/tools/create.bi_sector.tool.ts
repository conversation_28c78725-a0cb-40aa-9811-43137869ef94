import { Circle, Line, point, Segment, vector } from '@flatten-js/core';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import { geoDefaultHandlerFn, GeoErr } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, RenderAngle, RenderRay, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildBisectorConstruction, buildBisectorSegmentConstruction } from './util.construction';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isSamePoint,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Bisector Line Tool - Creates bisector lines from angles using selector DSL
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedAngle: RenderAngle | undefined;
    bisectorVector: number[] | undefined;
    rayPreview: RenderRay | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedAngle = undefined;
        this.bisectorVector = undefined;
        this.rayPreview = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic for angle + optional endpoint selection.
     */
    private createSelLogic() {
        // First selector: select an angle directly
        const angleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: enhanced vertex selector with or logic for vertex on stroke
        const vertexSelector = or(
            [
                // Option 1: Select free vertex with projection
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnBisectorRay(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToRayIntersection(stroke, previewVertex, doc),
                }),
            ],
            {
                flatten: true,
            }
        );

        // Main selection logic: first select angle, then select vertex
        this.selLogic = then([angleSelector, vertexSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [angle, endSelection] = selector.selected;
                this.performConstruction(doc, angle as RenderAngle, endSelection);
            },
        });
    }

    /**
     * Transform function to project any point onto the bisector ray
     */
    private projectOnBisectorRay(previewEl: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedAngle || !this.bisectorVector || !this.rayPreview) return previewEl; // Return original if no bisector is available

        try {
            const angleCoords = this.selectedAngle.coord('root', doc.rendererCtrl);
            const projectedCoords = projectPointOntoLine(previewEl.coords, angleCoords, this.bisectorVector);

            if (projectedCoords) {
                // Ensure the projected point is in the forward direction of the ray
                const pointVector = [projectedCoords[0] - angleCoords[0], projectedCoords[1] - angleCoords[1]];
                const dotProduct = pointVector[0] * this.bisectorVector[0] + pointVector[1] * this.bisectorVector[1];

                if (dotProduct >= 0) {
                    // Valid projection on the ray (not behind the start point)
                    previewEl.coords[0] = projectedCoords[0];
                    previewEl.coords[1] = projectedCoords[1];
                    if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
                } else {
                    // Point projects behind the ray start, project to the ray start itself
                    previewEl.coords[0] = angleCoords[0];
                    previewEl.coords[1] = angleCoords[1];
                    if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
                }
            }

            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto bisector ray:', error);
            return previewEl;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with bisector ray
     */
    private projectVertexOnStrokeToRayIntersection(
        stroke: any,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.selectedAngle || !this.bisectorVector || !this.rayPreview) {
            console.debug('Missing required data for intersection projection:', {
                hasAngle: !!this.selectedAngle,
                hasBisectorVector: !!this.bisectorVector,
                hasRayPreview: !!this.rayPreview,
            });
            return previewVertex;
        }

        try {
            // Get angle coordinates (ray start point)
            const angleCoords = this.selectedAngle.coord('root', doc.rendererCtrl);
            if (!angleCoords) {
                console.debug('Could not get angle coordinates');
                return previewVertex;
            }

            // Calculate intersection between the stroke and the bisector ray
            const intersectionPoint = this.calculateStrokeRayIntersection(
                stroke,
                angleCoords,
                this.bisectorVector,
                doc
            );

            if (intersectionPoint) {
                console.debug('Found intersection point:', intersectionPoint, 'for stroke type:', stroke.type);
                previewVertex.coords[0] = intersectionPoint[0];
                previewVertex.coords[1] = intersectionPoint[1];
                if (previewVertex.coords.length > 2) previewVertex.coords[2] = 0;
            } else {
                console.debug('No intersection found for stroke type:', stroke.type);
            }

            return previewVertex;
        } catch (error) {
            console.warn('Error projecting vertex on stroke to ray intersection:', error);
            return previewVertex;
        }
    }

    /**
     * Calculate intersection point between a stroke and the bisector ray
     */
    private calculateStrokeRayIntersection(
        stroke: any,
        rayStart: number[],
        rayDirection: number[],
        doc: GeoDocCtrl
    ): number[] | null {
        try {
            const rayStartPoint = point(rayStart[0], rayStart[1]);
            const rayVector = vector(rayDirection[0], rayDirection[1]).normalize();

            let intersections: any[] = [];

            // Create ray as line for intersection calculation (infinite line through ray start with ray direction)
            const rayLine = new Line(rayStartPoint, rayVector);

            // Handle different stroke types
            switch (stroke.type) {
                case 'RenderLine': {
                    const startCoords = stroke.coord('start', doc.rendererCtrl);
                    const endCoords = stroke.coord('end', doc.rendererCtrl);
                    const lineStart = point(startCoords[0], startCoords[1]);
                    const lineEnd = point(endCoords[0], endCoords[1]);
                    const strokeLine = new Line(lineStart, lineEnd);

                    intersections = rayLine.intersect(strokeLine);
                    break;
                }

                case 'RenderLineSegment':
                case 'RenderVector': {
                    const startCoords = stroke.coord('start', doc.rendererCtrl);
                    const endCoords = stroke.coord('end', doc.rendererCtrl);
                    const segmentStart = point(startCoords[0], startCoords[1]);
                    const segmentEnd = point(endCoords[0], endCoords[1]);
                    const strokeSegment = new Segment(segmentStart, segmentEnd);

                    intersections = rayLine.intersect(strokeSegment);
                    break;
                }

                case 'RenderRay': {
                    const startCoords = stroke.coord('start', doc.rendererCtrl);
                    const strokeVector = stroke.vector;
                    const strokeStart = point(startCoords[0], startCoords[1]);
                    const strokeDirection = vector(strokeVector[0], strokeVector[1]);
                    // For ray intersection, we'll use line approximation
                    const strokeLine = new Line(strokeStart, strokeDirection);

                    intersections = rayLine.intersect(strokeLine);
                    break;
                }

                case 'RenderCircle': {
                    const centerCoords = stroke.coord('center', doc.rendererCtrl);
                    const radius = stroke.radius;
                    const center = point(centerCoords[0], centerCoords[1]);
                    const strokeCircle = new Circle(center, radius);

                    intersections = rayLine.intersect(strokeCircle);
                    break;
                }

                default:
                    console.warn(`Unsupported stroke type for intersection: ${stroke.type}`);
                    return null;
            }

            if (!intersections || intersections.length === 0) return null;

            // Filter intersections to only those on the ray (not behind ray start)
            const validIntersections = intersections.filter(pt => {
                const toIntersection = vector(rayStartPoint, pt);
                const dotProduct = rayVector.dot(toIntersection);
                return dotProduct >= 0; // On ray, not behind
            });

            if (validIntersections.length === 0) return null;

            // Return closest valid intersection to ray start
            const closest = validIntersections.reduce((prev, curr) => {
                const prevDist = rayStartPoint.distanceTo(prev)[0];
                const currDist = rayStartPoint.distanceTo(curr)[0];
                return prevDist < currDist ? prev : curr;
            });

            return [closest.x, closest.y];
        } catch (error) {
            console.warn('Error calculating stroke-ray intersection:', error);
            return null;
        }
    }

    /**
     * Calculate bisector vector from two angle vectors using the RenderAngle API
     */
    private calculateBiSectorVectorOfAngle(angle: RenderAngle, ctrl: GeoDocCtrl): number[] {
        // Use the vector() method from RenderAngle to get direction vectors
        const startDirection = angle.vector('start', ctrl.rendererCtrl);
        const endDirection = angle.vector('end', ctrl.rendererCtrl);

        if (!startDirection || !endDirection) {
            throw new Error('Could not get direction vectors from angle');
        }

        // Create unit vectors using @flatten-js/core directly
        const vecA = vector(startDirection[0], startDirection[1]).normalize();
        const vecB = vector(endDirection[0], endDirection[1]).normalize();

        // Calculate angle directions based on line orientations
        const startVDir = angle.startVDir || 1;
        const endVDir = angle.endVDir || 1;

        // Apply direction multipliers
        const directedVecA = vecA.multiply(startVDir);
        const directedVecB = vecB.multiply(endVDir);

        // Calculate bisector as normalized sum
        const bisectorVec = directedVecA.add(directedVecB).normalize();

        return [bisectorVec.x, bisectorVec.y];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);

        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const [angle, endSelection] = this.selLogic.selected;

            if (angle && !endSelection) {
                // First selection: angle selected, show ray preview and update bisector info
                this.selectedAngle = angle as RenderAngle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(this.selectedAngle, ctrl);
                this.showBisectorRayPreview(ctrl, this.selectedAngle);
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only angle selected (not array yet)
            const angle = this.selLogic.selected as RenderAngle;
            if (angle && angle.type === 'RenderAngle') {
                this.selectedAngle = angle;
                this.bisectorVector = this.calculateBiSectorVectorOfAngle(this.selectedAngle, ctrl);
                this.showBisectorRayPreview(ctrl, this.selectedAngle);
            }
        }

        this.pQ.flush(ctrl);
    }

    /**
     * Show ray preview of the bisector line using the RenderAngle coord() method
     */
    private showBisectorRayPreview(ctrl: GeoDocCtrl, angle: RenderAngle) {
        // Calculate bisector vector
        this.selectedAngle = angle;
        this.bisectorVector = this.calculateBiSectorVectorOfAngle(angle, ctrl);

        // Get the angle vertex point using the coord() method
        const angleCoords = angle.coord('root', ctrl.rendererCtrl);
        if (!angleCoords) return;

        // Create ray preview with normalized direction
        const startPoint = [angleCoords[0], angleCoords[1]];
        this.rayPreview = pLine(ctrl, -20, RenderRay, startPoint, undefined, this.bisectorVector) as RenderRay;

        // Add to preview queue
        this.pQ.add(this.rayPreview);
    }

    /**
     * Performs the bisector construction based on selected elements
     * Using the same pattern as other tools (assignNames + remoteConstruct)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(ctrl: GeoDocCtrl, angle: RenderAngle, endElement?: any) {
        try {
            // Store selected angle and calculate bisector vector
            this.selectedAngle = angle;
            this.bisectorVector = this.calculateBiSectorVectorOfAngle(angle, ctrl);

            // Validate essential prerequisites
            if (!this.rayPreview) {
                console.error('rayPreview is not available for construction');
                this.resetState();
                return;
            }

            let actualElement = endElement;
            if (Array.isArray(endElement)) actualElement = endElement[0];

            if (actualElement && actualElement.type === 'RenderVertex') {
                const pointEnd = actualElement as RenderVertex;

                // Get angle point coordinates using the coord() method
                const angleCoords = angle.coord('root', ctrl.rendererCtrl);
                if (!angleCoords) {
                    this.resetState();
                    return;
                }

                if (isSamePoint(pointEnd.coords, angleCoords, ctrl)) {
                    // If vertex is the same as angle origin, use simple ray construction
                    await assignNames(
                        ctrl,
                        [],
                        this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                        '',
                        'Tia phân giác',
                        this.rayPreview
                    );

                    const construction = buildBisectorConstruction(angle.name);
                    construction.name = this.rayPreview.name;

                    await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, 'tia phân giác');
                } else {
                    // Bisector segment to point - assign names for both ray and endpoint
                    const { pcs, points } = await assignNames(
                        ctrl,
                        [pointEnd],
                        this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                        'Tên điểm cuối',
                        'Tên tia phân giác'
                    );

                    if (!pcs || !points) {
                        this.resetState();
                        return;
                    }

                    pointEnd.name = points[0].name;

                    const anglePoint = ctrl.rendererCtrl.elementAt(angle.anglePointIdx) as RenderVertex;
                    if (!anglePoint) throw new GeoErr('Angle root point not found');

                    // Calculate scaling factor - k should be the ratio of actual distance to unit vector
                    // Since bisectorVector is normalized, we can use the distance directly
                    const startPt = point(angleCoords[0], angleCoords[1]);
                    const endPt = point(pointEnd.coords[0], pointEnd.coords[1]);

                    let k = 0;
                    if (startPt && endPt) {
                        // k is simply the distance from angle vertex to end point
                        k = startPt.distanceTo(endPt)[0];
                    }

                    // Create combined name for angle and end element
                    const combinedName = `${anglePoint.name}${pointEnd.name}`;
                    const construction = buildBisectorSegmentConstruction(combinedName, angle.name, k);

                    await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, 'tia phân giác');
                }
            } else {
                // No valid end element selected - this should not happen due to selector logic
                console.warn('No valid end element provided for bisector construction');
                this.resetState();
            }
        } catch (error) {
            console.error('Error in bisector construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }
}
